{"name": "doctor-recep-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3004", "build": "next build --turbopack", "start": "next start -p 3004", "lint": "next lint", "create-admin": "node database/create/create-admin-user.js", "test-quota": "node tests/quota-system-test.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.800.0", "@dotlottie/react-player": "^1.6.19", "@google-cloud/pubsub": "^5.1.0", "@hookform/resolvers": "^5.0.1", "@opentelemetry/core": "^2.0.1", "@opentelemetry/sdk-trace-base": "^2.0.1", "@portabletext/react": "^3.2.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@sanity/block-content-to-react": "^3.0.0", "@sanity/client": "^7.6.0", "@sanity/image-url": "^1.1.0", "@sentry/nextjs": "^9.31.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@tiptap/extension-document": "^3.0.1", "@tiptap/extension-paragraph": "^3.0.1", "@tiptap/extension-text": "^3.0.1", "@tiptap/react": "^3.0.1", "@tiptap/starter-kit": "^3.0.1", "@upstash/redis": "^1.35.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "form-data": "^4.0.2", "framer-motion": "^12.23.6", "geist": "^1.4.2", "immer": "^10.1.1", "jose": "^6.0.11", "lucide-react": "^0.511.0", "next": "15.3.2", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "readline": "^1.3.0", "redis": "^5.5.6", "tailwind-merge": "^3.3.1", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5", "wrangler": "^4.19.1"}}