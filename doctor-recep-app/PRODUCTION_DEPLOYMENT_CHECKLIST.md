# 🚀 CELER AI PRODUCTION DEPLOYMENT CHECKLIST

## ✅ PRE-DEPLOYMENT CHECKLIST

### 1. Environment Configuration
- [ ] Copy `.env.production.template` to `.env.local` (frontend)
- [ ] Copy `python-backend/.env.production.template` to `python-backend/.env` (backend)
- [ ] Update all placeholder values with production credentials
- [ ] Verify all API keys are production-ready
- [ ] Generate new secure tokens for production

### 2. Database & Storage
- [ ] Production Supabase instance configured
- [ ] Database migrations applied (`add-referral-to-info-view.sql`)
- [ ] Production Redis instance configured
- [ ] Production Cloudflare R2 bucket configured
- [ ] Test database connections

### 3. Google Cloud Setup
- [ ] Production GCP project created (`celerai-prod`)
- [ ] Cloud Run API enabled
- [ ] Cloud Build API enabled
- [ ] Pub/Sub topic created (`generate-summary-jobs`)
- [ ] Service account permissions configured

### 4. Domain & SSL
- [ ] Production domain configured
- [ ] SSL certificates installed
- [ ] DNS records updated
- [ ] CDN configured (if applicable)

### 5. Monitoring & Logging
- [ ] Sentry production project configured
- [ ] Error tracking enabled
- [ ] Performance monitoring enabled
- [ ] Log aggregation configured

## 🚀 DEPLOYMENT STEPS

### Backend Deployment (Cloud Run)
```bash
# 1. Set environment variables
export PROJECT_ID="celerai-prod"
export GEMINI_API_KEY="your-production-gemini-key"
export FRONTEND_URL="https://your-production-domain.com"
export REGION="asia-south1"

# 2. Navigate to backend directory
cd python-backend

# 3. Deploy using the script
./deploy.sh
```

### Frontend Deployment (Vercel)
```bash
# 1. Install Vercel CLI (if not installed)
npm i -g vercel

# 2. Deploy to production
vercel --prod

# 3. Set environment variables in Vercel dashboard
# Upload the production .env.local values to Vercel
```

## ✅ POST-DEPLOYMENT VERIFICATION

### 1. Health Checks
- [ ] Backend health endpoint: `https://celer-ai-backend-prod-[hash]-uc.a.run.app/health`
- [ ] Frontend loads successfully
- [ ] Authentication flow works
- [ ] Database connections established

### 2. Core Functionality Tests
- [ ] User registration/login
- [ ] Audio upload and processing
- [ ] AI generation via Pub/Sub
- [ ] Real-time updates
- [ ] File storage (R2)
- [ ] Email notifications

### 3. Performance Tests
- [ ] Page load times < 3 seconds
- [ ] API response times < 2 seconds
- [ ] Audio processing completes successfully
- [ ] Concurrent user handling

### 4. Security Verification
- [ ] HTTPS enforced
- [ ] API authentication working
- [ ] CORS configured correctly
- [ ] Rate limiting enabled
- [ ] Input validation working

## 🔧 CONFIGURATION UPDATES NEEDED

### Frontend (.env.local)
```bash
# Update these values for production:
NEXT_PUBLIC_API_URL="https://celer-ai-backend-prod-[hash]-uc.a.run.app"
NEXT_PUBLIC_SUPABASE_URL="https://your-prod-supabase.supabase.co"
GOOGLE_CLOUD_PROJECT_ID="celerai-prod"
NODE_ENV="production"
```

### Backend (.env)
```bash
# Update these values for production:
ENVIRONMENT="production"
FRONTEND_URL="https://your-production-domain.com"
GOOGLE_CLOUD_PROJECT_ID="celerai-prod"
SUPABASE_URL="https://your-prod-supabase.supabase.co"
```

## 🚨 CRITICAL NOTES

1. **Service Name Change**: Backend now deploys as `celer-ai-backend-prod` (won't conflict with existing `doctor-recep-api`)

2. **Pub/Sub Architecture**: Production uses Pub/Sub for AI generation (already configured)

3. **Database Views**: Run `add-referral-to-info-view.sql` on production database

4. **Environment Isolation**: All production configs are separate from development

5. **Monitoring**: Set up alerts for service health and error rates

## 📞 ROLLBACK PLAN

If deployment fails:
1. Revert to previous Cloud Run revision
2. Update DNS to point to backup instance
3. Restore database from latest backup
4. Notify users of temporary maintenance

## 🎯 SUCCESS CRITERIA

- [ ] All health checks pass
- [ ] Zero critical errors in logs
- [ ] Response times within SLA
- [ ] All integrations working
- [ ] User flows complete successfully

---

**Deployment Date**: ___________
**Deployed By**: ___________
**Version**: ___________
**Rollback Plan Tested**: [ ] Yes [ ] No
