-- Add referral data to info_data_view - SAFE UPDATE
-- This script safely updates the existing view without breaking functionality

CREATE OR REPLACE VIEW info_data_view AS
SELECT
  p.id,
  p.name,
  p.email,
  p.role,
  p.monthly_quota,
  p.quota_used,
  p.billing_status,
  p.referral_code,
  p.approved,
  p.created_at,
  p.updated_at,

  -- EXISTING: Recent consultations as JSON array (last 15)
  (
    SELECT json_agg(
      json_build_object(
        'id', c.id,
        'patient_name', c.patient_name,
        'consultation_type', c.consultation_type,
        'status', c.status,
        'created_at', c.created_at,
        'updated_at', c.updated_at,
        'primary_audio_url', c.primary_audio_url,
        'ai_generated_note_json', c.ai_generated_note_json,
        'edited_note_json', c.edited_note_json
      ) ORDER BY c.created_at DESC
    )
    FROM (
      SELECT * FROM consultations
      WHERE doctor_id = p.id
      ORDER BY created_at DESC
      LIMIT 15
    ) c
  ) as recent_consultations,

  -- EXISTING: Consultation statistics as JSON object
  (
    SELECT json_build_object(
      'total_consultations', COUNT(*),
      'pending_consultations', COUNT(*) FILTER (WHERE status = 'pending_generation'),
      'generated_consultations', COUNT(*) FILTER (WHERE status = 'generated'),
      'approved_consultations', COUNT(*) FILTER (WHERE status = 'approved'),
      'today_consultations', COUNT(*) FILTER (WHERE DATE(created_at) = CURRENT_DATE)
    )
    FROM consultations
    WHERE doctor_id = p.id
  ) as consultation_stats,

  -- NEW: Referral data as JSON object
  (
    SELECT json_build_object(
      'referral_code', p.referral_code,
      'total_referrals', COALESCE(p.total_referrals, 0),
      'successful_referrals', COALESCE(p.successful_referrals, 0),
      'discount_earned', COALESCE(p.referral_discount_earned, 0),
      'pending_referrals', COALESCE(
        (SELECT COUNT(*) FROM referral_analytics WHERE referrer_id = p.id AND status = 'pending'),
        0
      ),
      'referred_by', CASE
        WHEN p.referred_by IS NOT NULL THEN
          (SELECT json_build_object(
            'name', ref.name,
            'referral_code', ref.referral_code
          ) FROM profiles ref WHERE ref.id = p.referred_by)
        ELSE NULL
      END,
      'recent_referrals', COALESCE(
        (SELECT json_agg(
          json_build_object(
            'id', ra.id,
            'name', ref_doc.name,
            'email', ref_doc.email,
            'signup_date', ra.signup_date,
            'conversion_date', ra.conversion_date,
            'status', ra.status
          ) ORDER BY ra.signup_date DESC
        )
        FROM referral_analytics ra
        JOIN profiles ref_doc ON ref_doc.id = ra.referred_doctor_id
        WHERE ra.referrer_id = p.id
        LIMIT 10),
        '[]'::json
      )
    )
  ) as referral_data

FROM profiles p
WHERE p.role = 'doctor' AND p.approved = true;