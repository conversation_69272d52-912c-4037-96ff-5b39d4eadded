-- EXACT script to add referral data to info_data_view
-- Run this AFTER run-migration.sql has been executed successfully

CREATE OR REPLACE VIEW info_data_view AS
SELECT 
  p.id,
  p.name,
  p.email,
  p.role,
  p.monthly_quota,
  p.quota_used,
  p.billing_status,
  p.referral_code,
  p.approved,
  p.created_at,
  p.updated_at,
  
  -- Referral data (NEW)
  p.total_referrals,
  p.successful_referrals,
  p.referral_discount_earned,
  p.referred_by,
  
  -- Recent consultations as JSON array (last 15)
  (
    SELECT json_agg(
      json_build_object(
        'id', c.id,
        'patient_name', c.patient_name,
        'consultation_type', c.consultation_type,
        'status', c.status,
        'created_at', c.created_at,
        'updated_at', c.updated_at,
        'primary_audio_url', c.primary_audio_url,
        'ai_generated_note_json', c.ai_generated_note_json,
        'edited_note_json', c.edited_note_json
      ) ORDER BY c.created_at DESC
    )
    FROM (
      SELECT * FROM consultations 
      WHERE doctor_id = p.id
      ORDER BY created_at DESC 
      LIMIT 15
    ) c
  ) as recent_consultations,
  
  -- Consultation statistics as JSON object
  (
    SELECT json_build_object(
      'total_consultations', COUNT(*),
      'pending_consultations', COUNT(*) FILTER (WHERE status = 'pending_generation'),
      'generated_consultations', COUNT(*) FILTER (WHERE status = 'generated'),
      'approved_consultations', COUNT(*) FILTER (WHERE status = 'approved'),
      'today_consultations', COUNT(*) FILTER (WHERE DATE(created_at) = CURRENT_DATE)
    )
    FROM consultations 
    WHERE doctor_id = p.id
  ) as consultation_stats,
  
  -- Pending referrals count (NEW)
  (
    SELECT COUNT(*)
    FROM referral_analytics 
    WHERE referrer_id = p.id AND status = 'pending'
  ) as pending_referrals,
  
  -- Recent referrals data (NEW)
  (
    SELECT json_agg(
      json_build_object(
        'id', ra.id,
        'name', rp.name,
        'email', rp.email,
        'signup_date', ra.signup_date,
        'conversion_date', ra.conversion_date,
        'status', ra.status
      ) ORDER BY ra.signup_date DESC
    )
    FROM (
      SELECT * FROM referral_analytics 
      WHERE referrer_id = p.id
      ORDER BY signup_date DESC 
      LIMIT 10
    ) ra
    LEFT JOIN profiles rp ON ra.referred_doctor_id = rp.id
  ) as recent_referrals

FROM profiles p
WHERE p.role = 'doctor' AND p.approved = true;
