import { createServerClient } from '@supabase/ssr'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll()
          },
          setAll(cookiesToSet) {
            // No need to set cookies in GET request
          },
        },
      }
    )

    const {
      data: { user },
      error
    } = await supabase.auth.getUser()

    if (error || !user) {
      return NextResponse.json({ user: null }, { status: 401 })
    }

    // Get user profile to check approval status
    const { data: profile } = await supabase
      .from('profiles')
      .select('approved, role, full_name, email')
      .eq('id', user.id)
      .single()

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        approved: profile?.approved || false,
        role: profile?.role || 'user',
        full_name: profile?.full_name || user.user_metadata?.full_name || '',
      }
    })

  } catch (error) {
    console.error('Session API error:', error)
    return NextResponse.json({ user: null }, { status: 500 })
  }
}
