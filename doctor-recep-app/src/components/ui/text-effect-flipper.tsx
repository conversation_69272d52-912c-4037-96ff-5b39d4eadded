"use client"

import React from "react"
import { motion } from "framer-motion"

import { cn } from "@/lib/utils"

interface FlipLinkProps {
  children: React.ReactNode
  href?: string
  className?: string
  onClick?: () => void
}

const DURATION = 0.25
const STAGGER = 0.025

export default function FlipLink({
  children,
  href,
  className,
  onClick,
}: FlipLinkProps) {
  const text = typeof children === "string" ? children : ""
  
  const Component = href ? "a" : "button"
  
  return (
    <Component
      href={href}
      onClick={onClick}
      className={cn(
        "relative block overflow-hidden whitespace-nowrap text-4xl font-black uppercase sm:text-7xl md:text-8xl lg:text-9xl",
        "hover:text-primary transition-colors duration-300",
        className
      )}
      style={{
        lineHeight: 0.75,
      }}
    >
      <div>
        {text.split("").map((l, i) => (
          <motion.span
            variants={{
              initial: {
                y: 0,
              },
              hovered: {
                y: "-100%",
              },
            }}
            transition={{
              duration: DURATION,
              ease: "easeInOut",
              delay: STAGGER * i,
            }}
            className="inline-block"
            key={i}
          >
            {l}
          </motion.span>
        ))}
      </div>
      <div className="absolute inset-0">
        {text.split("").map((l, i) => (
          <motion.span
            variants={{
              initial: {
                y: "100%",
              },
              hovered: {
                y: 0,
              },
            }}
            transition={{
              duration: DURATION,
              ease: "easeInOut",
              delay: STAGGER * i,
            }}
            className="inline-block"
            key={i}
          >
            {l}
          </motion.span>
        ))}
      </div>
    </Component>
  )
}

// For non-string children, render without flip effect
function FlipLinkWrapper({ children, href, className, onClick }: FlipLinkProps) {
  if (typeof children === "string") {
    return (
      <motion.div
        initial="initial"
        whileHover="hovered"
        className="group"
      >
        <FlipLink href={href} className={className} onClick={onClick}>
          {children}
        </FlipLink>
      </motion.div>
    )
  }
  
  const Component = href ? "a" : "div"
  return (
    <Component
      href={href}
      onClick={onClick}
      className={cn("hover:text-primary transition-colors duration-300", className)}
    >
      {children}
    </Component>
  )
}

export { FlipLinkWrapper as FlipLink }
