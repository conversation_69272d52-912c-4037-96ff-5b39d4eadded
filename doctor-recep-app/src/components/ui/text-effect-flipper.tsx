"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

interface FlipLinkProps {
  children: React.ReactNode
  href?: string
  className?: string
  onClick?: () => void
}

export default function FlipLink({ children, href, className, onClick }: FlipLinkProps) {
  const content = (
    <motion.span
      className={cn(
        "relative inline-block overflow-hidden cursor-pointer",
        className
      )}
      whileHover="hover"
      onClick={onClick}
    >
      <motion.span
        className="block"
        variants={{
          hover: {
            y: "-100%",
            transition: {
              duration: 0.3,
              ease: [0.25, 0.46, 0.45, 0.94]
            }
          }
        }}
      >
        {children}
      </motion.span>
      <motion.span
        className="absolute inset-0 block"
        variants={{
          hover: {
            y: "0%",
            transition: {
              duration: 0.3,
              ease: [0.25, 0.46, 0.45, 0.94]
            }
          }
        }}
        initial={{ y: "100%" }}
      >
        {children}
      </motion.span>
    </motion.span>
  )

  if (href) {
    return (
      <a href={href} className="inline-block">
        {content}
      </a>
    )
  }

  return content
}
