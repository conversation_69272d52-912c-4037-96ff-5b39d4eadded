import React, { ReactNode } from "react"
import { motion } from "framer-motion"

const DURATION = 0.25
const STAGGER = 0.025

interface FlipLinkProps {
  children: string
  href?: string
  className?: string
  size?: "sm" | "md" | "lg" | "hero"
}

const FlipLink: React.FC<FlipLinkProps> = ({ children, href, className, size = "lg" }) => {
  const sizeClasses = {
    sm: "text-sm",
    md: "text-lg",
    hero: "text-4xl font-extrabold tracking-tighter sm:text-5xl lg:text-6xl", // Hero section sizing
    lg: "text-4xl font-black uppercase sm:text-7xl md:text-8xl lg:text-9xl"
  }
  const content = (
    <motion.div
      initial="initial"
      whileHover="hovered"
      className={`relative block overflow-hidden whitespace-nowrap ${sizeClasses[size]} ${className || ""}`}
      style={{
        lineHeight: size === "lg" ? 0.75 : size === "hero" ? 1.1 : 1,
      }}
    >
      <div>
        {children.split("").map((l, i) => (
          <motion.span
            variants={{
              initial: {
                y: 0,
              },
              hovered: {
                y: "-100%",
              },
            }}
            transition={{
              duration: DURATION,
              ease: "easeInOut",
              delay: STAGGER * i,
            }}
            className="inline-block"
            key={i}
          >
            {l}
          </motion.span>
        ))}
      </div>
      <div className="absolute inset-0">
        {children.split("").map((l, i) => (
          <motion.span
            variants={{
              initial: {
                y: "100%",
              },
              hovered: {
                y: 0,
              },
            }}
            transition={{
              duration: DURATION,
              ease: "easeInOut",
              delay: STAGGER * i,
            }}
            className="inline-block"
            key={i}
          >
            {l}
          </motion.span>
        ))}
      </div>
    </motion.div>
  )

  if (href) {
    return (
      <a href={href}>
        {content}
      </a>
    )
  }

  return content
}

export default FlipLink
