'use client'

import { forwardRef, useCallback, useEffect, useMemo, useState, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { useConsultationStore } from '@/lib/stores/consultation-store'
import { getSchemaForConsultationType } from '@/lib/schemas/consultation-note'
import { Input } from '@/components/ui/input'
import { AutoSaveIndicator } from '@/components/ui/autosave-indicator'
import { AutoSaveStatus } from '@/hooks/useAutosave'
import { Button } from '@/components/ui/button'

// Simple debounce implementation
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
  let timeout: NodeJS.Timeout
  return ((...args: any[]) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }) as T
}

interface SeamlessInlineEditorProps {
  consultationData: any
  consultationType: string
  onUpdate?: (data: any) => void
  readOnly?: boolean
  className?: string
}

interface EditableFieldProps {
  value: any
  path: string
  fieldSchema: any
  onUpdate: (path: string, value: any) => void
  readOnly: boolean
  label: string
}

const EditableField = ({ value, path, fieldSchema, onUpdate, readOnly, label }: EditableFieldProps) => {
  const [isEditing, setIsEditing] = useState(false)
  const [localValue, setLocalValue] = useState(value)
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null)
  const displayRef = useRef<HTMLDivElement>(null)

  const fieldType = fieldSchema?.type || 'string'
  // Always use multiline editing for consistent behavior
  const isMultiline = true

  useEffect(() => {
    setLocalValue(value)
  }, [value])

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      if (inputRef.current instanceof HTMLInputElement) {
        inputRef.current.select()
      }
      // Auto-resize textarea to content
      if (inputRef.current instanceof HTMLTextAreaElement) {
        const textarea = inputRef.current
        textarea.style.height = 'auto'
        textarea.style.height = textarea.scrollHeight + 'px'
      }
    }
  }, [isEditing])

  // Auto-resize on content change
  useEffect(() => {
    if (isEditing && inputRef.current instanceof HTMLTextAreaElement) {
      const textarea = inputRef.current
      textarea.style.height = 'auto'
      textarea.style.height = textarea.scrollHeight + 'px'
    }
  }, [localValue, isEditing])

  const handleClick = () => {
    if (!readOnly) {
      setIsEditing(true)
    }
  }

  const handleSave = () => {
    setIsEditing(false)
    if (localValue !== value) {
      onUpdate(path, localValue)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isMultiline) {
      e.preventDefault()
      handleSave()
    } else if (e.key === 'Escape') {
      setLocalValue(value)
      setIsEditing(false)
    }
  }

  const handleBlur = () => {
    handleSave()
  }

  const displayValue = value || 'Click to edit...'
  const isEmpty = !value || value === ''

  if (isEditing) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="relative w-full"
      >
        {isMultiline ? (
          <textarea
            ref={inputRef as React.RefObject<HTMLTextAreaElement>}
            value={localValue || ''}
            onChange={(e) => setLocalValue(e.target.value)}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            className="w-full resize-none border-none bg-transparent outline-none"
            style={{
              fontFamily: 'inherit',
              fontSize: 'inherit',
              lineHeight: 'inherit',
              padding: '4px 8px', // Match display mode padding (py-1 px-2)
              margin: '0',
              height: 'auto',
              minHeight: 'auto',
              textAlign: 'inherit',
              wordWrap: 'break-word',
              whiteSpace: 'pre-wrap',
              overflow: 'hidden'
            }}
            placeholder={`Enter ${label.toLowerCase()}...`}
          />
        ) : (
          <input
            ref={inputRef as React.RefObject<HTMLInputElement>}
            value={localValue || ''}
            onChange={(e) => setLocalValue(e.target.value)}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            className="w-full border-none bg-transparent outline-none"
            style={{
              fontFamily: 'inherit',
              fontSize: 'inherit',
              lineHeight: 'inherit',
              padding: '4px 8px', // Match display mode padding (py-1 px-2)
              margin: '0',
              height: 'auto',
              textAlign: 'inherit'
            }}
            placeholder={`Enter ${label.toLowerCase()}...`}
          />
        )}
      </motion.div>
    )
  }

  return (
    <motion.div
      ref={displayRef}
      onClick={handleClick}
      className={cn(
        "w-full min-h-[32px] cursor-pointer rounded-md px-2 py-1 transition-all duration-200",
        "hover:bg-accent dark:hover:bg-accent hover:border hover:border-border",
        isEmpty && "text-muted-foreground italic",
        !readOnly && "hover:shadow-sm",
        "flex items-center"
      )}
      whileHover={{ scale: 1.005 }}
      whileTap={{ scale: 0.995 }}
    >
      <span className="break-words">{displayValue}</span>
    </motion.div>
  )
}

export const SeamlessInlineEditor = forwardRef<HTMLDivElement, SeamlessInlineEditorProps>(({
  consultationData,
  consultationType,
  onUpdate,
  readOnly = false,
  className = ''
}, ref) => {
  const {
    setUnsavedChanges,
    saveEditedNote,
    isSaving,
    hasUnsavedChanges,
    lastSaved
  } = useConsultationStore()

  // Get schema for this consultation type
  const schema = useMemo(() =>
    getSchemaForConsultationType(consultationType),
    [consultationType]
  )

  // Debounced save function
  const debouncedSave = useMemo(
    () => debounce(async (data: any) => {
      try {
        await saveEditedNote(data)
      } catch (error) {
        console.error('Auto-save failed:', error)
      }
    }, 2000),
    [saveEditedNote]
  )

  // Handle field updates
  const handleFieldUpdate = useCallback((path: string, value: any) => {
    if (readOnly) return

    // Note: Undo functionality moved to simple reset button

    const pathParts = path.split('.')
    const updatedData = JSON.parse(JSON.stringify(consultationData))

    let current = updatedData
    for (let i = 0; i < pathParts.length - 1; i++) {
      if (!current[pathParts[i]]) {
        current[pathParts[i]] = {}
      }
      current = current[pathParts[i]]
    }

    current[pathParts[pathParts.length - 1]] = value

    // Mark as having unsaved changes
    setUnsavedChanges(true)

    // Trigger callback
    onUpdate?.(updatedData)

    // Auto-save
    debouncedSave(updatedData)
  }, [consultationData, onUpdate, setUnsavedChanges, debouncedSave, readOnly])

  // Get ordered fields based on schema definition
  const getOrderedFields = useCallback((data: any, schema: any) => {
    if (!schema?.shape || !data) return []

    // Get the order from schema definition (Zod schema maintains order)
    const schemaKeys = Object.keys(schema.shape)
    const dataKeys = Object.keys(data)

    // First add fields in schema order, then any extra fields from data
    const orderedKeys = [
      ...schemaKeys.filter(key => key in data),
      ...dataKeys.filter(key => !schemaKeys.includes(key))
    ]

    return orderedKeys.map(key => [key, data[key]])
  }, [])

  // Render medications as a dynamic table
  const renderMedicationsTable = (medications: any[], path: string, key: string = 'medications_on_discharge') => {
    if (!Array.isArray(medications) || medications.length === 0) {
      return (
        <div className="mb-4">
          <div className="grid grid-cols-[200px_1fr] gap-4 items-start">
            <span className="text-sm font-medium text-muted-foreground pt-2">
              {key === 'prescription' ? 'Prescription:' : 'Medications On Discharge:'}
            </span>
            <div className="text-muted-foreground italic">No medications prescribed</div>
          </div>
        </div>
      )
    }

    return (
      <div key={path} className="mb-6">
        <div className="grid grid-cols-[200px_1fr] gap-4 items-start">
          <span className="text-sm font-medium text-muted-foreground pt-2">
            {key === 'prescription' ? 'Prescription:' : 'Medications On Discharge:'}
          </span>
          <div className="border border-border rounded-lg overflow-hidden">
            <table className="w-full">
              <thead className="bg-muted">
                <tr>
                  <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase">Medicine</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase">Dosage</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase">Frequency</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase">Duration</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-border">
                {medications.map((med, index) => (
                  <tr key={index} className="hover:bg-accent">
                    <td className="px-3 py-2">
                      <EditableField
                        value={med.drug_name || med.medicine || ''}
                        path={`${path}.${index}.drug_name`}
                        fieldSchema={{ type: 'string' }}
                        onUpdate={handleFieldUpdate}
                        readOnly={readOnly}
                        label="Medicine"
                      />
                    </td>
                    <td className="px-3 py-2">
                      <EditableField
                        value={med.dose || med.dosage || ''}
                        path={`${path}.${index}.dose`}
                        fieldSchema={{ type: 'string' }}
                        onUpdate={handleFieldUpdate}
                        readOnly={readOnly}
                        label="Dosage"
                      />
                    </td>
                    <td className="px-3 py-2">
                      <EditableField
                        value={med.frequency || ''}
                        path={`${path}.${index}.frequency`}
                        fieldSchema={{ type: 'string' }}
                        onUpdate={handleFieldUpdate}
                        readOnly={readOnly}
                        label="Frequency"
                      />
                    </td>
                    <td className="px-3 py-2">
                      <EditableField
                        value={med.duration || ''}
                        path={`${path}.${index}.duration`}
                        fieldSchema={{ type: 'string' }}
                        onUpdate={handleFieldUpdate}
                        readOnly={readOnly}
                        label="Duration"
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    )
  }

  // Render field based on schema with consistent alignment
  const renderField = (key: string, value: any, fieldSchema: any, path: string = '') => {
    const fullPath = path ? `${path}.${key}` : key
    const label = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())

    // Skip schema_version and other technical fields
    if (key === 'schema_version') return null

    // Special handling for medications and prescriptions
    if (key === 'medications_on_discharge' || key === 'medications' || key === 'prescription') {
      return renderMedicationsTable(value || [], fullPath, key)
    }

    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      return (
        <div key={fullPath} className="mb-6">
          <h3 className="text-lg font-semibold text-primary mb-4 border-b border-border pb-2">
            {label}
          </h3>
          <div className="ml-4 space-y-4">
            {getOrderedFields(value, fieldSchema).map(([subKey, subValue]) =>
              renderField(subKey, subValue, fieldSchema?.properties?.[subKey], fullPath)
            )}
          </div>
        </div>
      )
    }

    if (Array.isArray(value)) {
      return (
        <div key={fullPath} className="mb-4">
          <div className="grid grid-cols-[200px_1fr] gap-4 items-start">
            <span className="text-sm font-medium text-muted-foreground pt-2">
              {label}:
            </span>
            <div className="space-y-2">
              {value.map((item, index) => (
                <div key={`${fullPath}.${index}`} className="flex items-start space-x-2">
                  <span className="text-muted-foreground mt-1">•</span>
                  <EditableField
                    value={typeof item === 'object' ? JSON.stringify(item, null, 2) : item}
                    path={`${fullPath}.${index}`}
                    fieldSchema={fieldSchema?.items || { type: 'string' }}
                    onUpdate={handleFieldUpdate}
                    readOnly={readOnly}
                    label={`${label} ${index + 1}`}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      )
    }

    return (
      <div key={fullPath} className="mb-4">
        <div className="grid grid-cols-[200px_1fr] gap-4 items-start">
          <span className="text-sm font-medium text-muted-foreground pt-2">
            {label}:
          </span>
          <div className="min-h-[24px]">
            <EditableField
              value={value}
              path={fullPath}
              fieldSchema={fieldSchema}
              onUpdate={handleFieldUpdate}
              readOnly={readOnly}
              label={label}
            />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div ref={ref} className={cn("seamless-inline-editor", className)}>
      {/* Save status indicator and controls */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <AutoSaveIndicator
            status={
              isSaving
                ? 'saving'
                : hasUnsavedChanges
                  ? 'idle'
                  : lastSaved
                    ? 'saved'
                    : 'idle'
            }
            showText={true}
          />


        </div>
        {!readOnly && (
          <span className="text-xs text-muted-foreground">
            Auto-saves every 2 seconds
          </span>
        )}
      </div>

      {/* Seamless Content Area */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className={cn(
          "min-h-[500px] w-full rounded-xl border border-border bg-background p-6 shadow-lg",
          "focus-within:shadow-xl focus-within:ring-2 focus-within:ring-ring/30",
          "transition-all duration-300 ease-in-out"
        )}
      >
        <div className="prose prose-lg max-w-none dark:prose-invert">
          {schema && consultationData && getOrderedFields(consultationData, schema).map(([key, value]) =>
            renderField(key, value, (schema.shape as any)?.[key])
          )}
        </div>
      </motion.div>
    </div>
  )
})

SeamlessInlineEditor.displayName = 'SeamlessInlineEditor'
