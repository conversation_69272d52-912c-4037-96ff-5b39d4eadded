"use client"

import { useRef, useState } from "react"
import { motion, useInView } from "framer-motion"

import { cn } from "@/lib/utils"

type AnimationType = 
  | "rollIn"
  | "whipIn"
  | "fadeIn"
  | "popIn"
  | "fadeInUp"
  | "shiftInUp"
  | "whipInUp"
  | "calmInUp"

interface TextAnimateProps {
  text: string
  type: AnimationType
  className?: string
  delay?: number
  duration?: number
}

const animationVariants = {
  rollIn: {
    initial: { opacity: 0, rotateX: -90 },
    animate: { opacity: 1, rotateX: 0 },
  },
  whipIn: {
    initial: { opacity: 0, scale: 0.8, x: -50 },
    animate: { opacity: 1, scale: 1, x: 0 },
  },
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
  },
  popIn: {
    initial: { opacity: 0, scale: 0.5 },
    animate: { opacity: 1, scale: 1 },
  },
  fadeInUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
  },
  shiftInUp: {
    initial: { opacity: 0, y: 30, scale: 0.95 },
    animate: { opacity: 1, y: 0, scale: 1 },
  },
  whipInUp: {
    initial: { opacity: 0, y: 50, scale: 0.8 },
    animate: { opacity: 1, y: 0, scale: 1 },
  },
  calmInUp: {
    initial: { opacity: 0, y: 10 },
    animate: { opacity: 1, y: 0 },
  },
}

export default function TextAnimate({
  text,
  type,
  className,
  delay = 0,
  duration = 0.6,
}: TextAnimateProps) {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, amount: 0.3 })
  
  const variant = animationVariants[type]
  
  return (
    <motion.div
      ref={ref}
      initial={variant.initial}
      animate={isInView ? variant.animate : variant.initial}
      transition={{
        duration,
        delay,
        ease: type.includes("whip") ? [0.25, 0.46, 0.45, 0.94] : "easeOut",
        type: type.includes("pop") ? "spring" : "tween",
        stiffness: type.includes("pop") ? 300 : undefined,
        damping: type.includes("pop") ? 20 : undefined,
      }}
      className={cn("inline-block", className)}
    >
      {text}
    </motion.div>
  )
}

export { TextAnimate }
