"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

interface TextAnimateProps {
  text: string
  type?: "rollIn" | "whipIn" | "fadeIn" | "popIn" | "fadeInUp" | "shiftInUp" | "whipInUp" | "calmInUp"
  className?: string
  delay?: number
  duration?: number
}

const animationVariants = {
  rollIn: {
    hidden: { opacity: 0, rotateX: -90 },
    visible: { opacity: 1, rotateX: 0 }
  },
  whipIn: {
    hidden: { opacity: 0, x: -100, scale: 0.8 },
    visible: { opacity: 1, x: 0, scale: 1 }
  },
  fadeIn: {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  },
  popIn: {
    hidden: { opacity: 0, scale: 0.5 },
    visible: { opacity: 1, scale: 1 }
  },
  fadeInUp: {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  },
  shiftInUp: {
    hidden: { opacity: 0, y: 50, scale: 0.9 },
    visible: { opacity: 1, y: 0, scale: 1 }
  },
  whipInUp: {
    hidden: { opacity: 0, y: 100, rotateX: 90 },
    visible: { opacity: 1, y: 0, rotateX: 0 }
  },
  calmInUp: {
    hidden: { opacity: 0, y: 30 },
    visible: { opacity: 1, y: 0 }
  }
}

export function TextAnimate({
  text,
  type = "fadeIn",
  className,
  delay = 0,
  duration = 0.6
}: TextAnimateProps) {
  const words = text.split(" ")
  const variants = animationVariants[type]

  return (
    <motion.div
      className={cn("", className)}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      transition={{
        staggerChildren: 0.1,
        delayChildren: delay
      }}
    >
      {words.map((word, index) => (
        <motion.span
          key={index}
          className="inline-block mr-1"
          variants={variants}
          transition={{
            duration,
            ease: [0.25, 0.46, 0.45, 0.94]
          }}
        >
          {word}
        </motion.span>
      ))}
    </motion.div>
  )
}
