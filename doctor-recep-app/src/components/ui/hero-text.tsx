"use client"

import { motion } from "framer-motion"
import { TextAnimate } from "./text-animate"

interface HeroTextProps {
  text: string
  delay?: number
  className?: string
}

export function HeroText({ text, delay = 0, className = "" }: HeroTextProps) {
  return (
    <motion.div
      className={`relative inline-block overflow-hidden cursor-pointer ${className}`}
      whileHover="hover"
      initial="initial"
      animate="visible"
    >
      {/* Initial whipInUp animation */}
      <motion.div
        variants={{
          initial: { opacity: 0, y: 100, rotateX: 90 },
          visible: { opacity: 1, y: 0, rotateX: 0 },
        }}
        transition={{
          duration: 0.6,
          ease: [0.25, 0.46, 0.45, 0.94],
          delay
        }}
      >
        <TextAnimate
          text={text}
          type="whipInUp"
          className="inline-block"
          delay={delay}
        />
      </motion.div>

      {/* Flipper effect overlay */}
      <motion.div
        className="absolute inset-0"
        variants={{
          initial: { y: "100%" },
          hover: { y: "0%" }
        }}
        transition={{
          duration: 0.3,
          ease: [0.25, 0.46, 0.45, 0.94]
        }}
      >
        {text.split(" ").map((word, index) => (
          <motion.span
            key={index}
            className="inline-block mr-1"
            variants={{
              hover: {
                y: "-100%",
                transition: {
                  duration: 0.3,
                  ease: [0.25, 0.46, 0.45, 0.94],
                  delay: index * 0.05
                }
              }
            }}
          >
            {word}
          </motion.span>
        ))}
      </motion.div>
    </motion.div>
  )
}
